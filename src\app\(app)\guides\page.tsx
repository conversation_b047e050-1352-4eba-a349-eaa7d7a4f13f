import {
  BookOpen,
  Info,
  Layers3,
  DollarSign,
  TrendingUp,
  Home,
  LineChart,
  FileText
} from "lucide-react";

const guideGroups = [
  {
    title: "Understanding Loan Mechanics",
    guides: [
      {
        icon: <BookOpen className="w-7 h-7 text-blue-600" />,
        title: "How UK Student Loans Work",
        desc: "A comprehensive guide to the UK student loan system, plans, and repayment mechanics.",
        tags: [
          { label: "Beginner", color: "bg-blue-100 text-blue-700" },
          { label: "Essential", color: "bg-green-100 text-green-700" }
        ]
      },
      {
        icon: <Info className="w-7 h-7 text-blue-600" />,
        title: "Understanding Student Loan Interest",
        desc: "How interest rates work, how they're calculated, and their impact on your total repayments.",
        tags: [
          { label: "Intermediate", color: "bg-blue-100 text-blue-700" },
          { label: "Interest Rates", color: "bg-green-100 text-green-700" }
        ]
      },
      {
        icon: <Layers3 className="w-7 h-7 text-blue-600" />,
        title: "Repayment Thresholds Explained",
        desc: "A guide to income thresholds for all UK student loan plans and how they affect your monthly payments.",
        tags: [
          { label: "Beginner", color: "bg-blue-100 text-blue-700" },
          { label: "Repayments", color: "bg-green-100 text-green-700" }
        ]
      }
    ]
  },
  {
    title: "Salary & Scenario Guides",
    guides: [
      {
        icon: <DollarSign className="w-7 h-7 text-green-600" />,
        title: "Student Loans on Low Incomes",
        desc: "What happens if you are below the repayment threshold or have variable income.",
        tags: [{ label: "Income Planning", color: "bg-green-100 text-green-700" }]
      },
      {
        icon: <TrendingUp className="w-7 h-7 text-green-600" />,
        title: "Student Loans for High Earners",
        desc: "Strategic planning for graduates expecting high incomes and when early repayment makes sense.",
        tags: [{ label: "Strategy", color: "bg-green-100 text-green-700" }]
      },
      {
        icon: <Home className="w-7 h-7 text-green-600" />,
        title: "Student Loans & Mortgages",
        desc: "How student loan repayments affect mortgage applications and housing affordability.",
        tags: [{ label: "Home Buying", color: "bg-green-100 text-green-700" }]
      }
    ]
  },
  {
    title: "Repayment Strategy Guides",
    guides: [
      {
        icon: <LineChart className="w-7 h-7 text-purple-600" />,
        title: "Should You Overpay Your Student Loan?",
        desc: "In-depth analysis of when making additional payments makes financial sense.",
        tags: [{ label: "Financial Planning", color: "bg-purple-100 text-purple-700" }]
      },
      {
        icon: <TrendingUp className="w-7 h-7 text-purple-600" />,
        title: "Investing vs. Loan Repayment",
        desc: "Comparing the potential returns of investing versus paying off your student loan faster.",
        tags: [{ label: "Investment Strategy", color: "bg-purple-100 text-purple-700" }]
      },
      {
        icon: <FileText className="w-7 h-7 text-purple-600" />,
        title: "Student Loans & Tax Planning",
        desc: "Understanding how student loan repayments interact with income tax and pension contributions.",
        tags: [{ label: "Tax Optimisation", color: "bg-purple-100 text-purple-700" }]
      }
    ]
  }
];

const GuideTag = ({ label, color }) => (
  <span className={`px-2 py-0.5 rounded-md text-xs font-semibold mr-1 ${color}`}>{label}</span>
);

const GuideCard = ({ icon, title, desc, tags }) => (
  <div className="rounded-xl bg-white border border-gray-100 shadow py-5 px-5 flex flex-col h-full hover:shadow-lg transition cursor-pointer">
    <div className="w-11 h-11 rounded-lg bg-blue-50 flex items-center justify-center mb-3">{icon}</div>
    <div className="font-semibold text-lg mb-1 text-gray-900">{title}</div>
    <div className="text-gray-600 text-sm mb-3">{desc}</div>
    <div className="flex flex-wrap gap-1 mt-auto">
      {tags.map((tag, idx) => <GuideTag key={idx} {...tag} />)}
    </div>
  </div>
);

const StudentLoanGuidesPage = () => (
  <div className="min-h-screen bg-gray-50 pb-16">
    {/* Header */}
    <div className="max-w-4xl mx-auto text-sm text-gray-500 py-2 px-4">
      Home <span className="mx-1">›</span> Guides
    </div>
    <div className="rounded-2xl bg-blue-50 max-w-4xl mx-auto px-8 py-8 mb-8">
      <h1 className="text-3xl md:text-4xl font-bold mb-2 text-gray-900">Student Loan Guides</h1>
      <p className="text-gray-700 text-md max-w-3xl">
        Understand the UK student loan system with our comprehensive guides and resources
      </p>
    </div>
    {guideGroups.map(group => (
      <div className="max-w-4xl mx-auto mb-8" key={group.title}>
        <div className="font-bold text-md mb-4 text-gray-900">{group.title}</div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
          {group.guides.map((g, i) => <GuideCard {...g} key={i} />)}
        </div>
      </div>
    ))}
    {/* CTA */}
    <div className="max-w-3xl mx-auto mt-10">
      <div className="rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-8 text-white text-center shadow">
        <div className="text-2xl font-bold mb-2">Calculate Your Personal Repayments</div>
        <div className="mb-6">
          Use our suite of calculators to apply what you've learned to your specific situation.
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4 justify-center">
          <button className="bg-white text-blue-700 font-semibold rounded-md px-5 py-2 hover:bg-blue-50 border border-white transition">
            Try Our Calculators
          </button>
          <button className="bg-blue-700 text-white font-semibold rounded-md px-5 py-2 hover:bg-blue-800 border border-blue-700 transition">
            Compare Loan Plans
          </button>
        </div>
      </div>
    </div>
  </div>
);

export default StudentLoanGuidesPage;
