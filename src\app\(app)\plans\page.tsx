import React from "react";

const plans = [
  {
    type: "Plan 1",
    who: "English/Welsh students who started before Sept 2012",
    threshold: "£22,015 per year",
    interest: "RPI or Bank of England base rate + 1% (whichever is lower)",
    writeoff: "25 years from repayment start date"
  },
  {
    type: "Plan 2",
    who: "English/Welsh students who started between Sept 2012 - Aug 2023",
    threshold: "£27,295 per year",
    interest: "RPI + 3% depending on income",
    writeoff: "30 years from repayment start date"
  },
  {
    type: "Plan 4",
    who: "Scottish students who started after Sept 1998",
    threshold: "£27,660 per year",
    interest: "RPI or Bank of England base rate + 1% (whichever is lower)",
    writeoff: "30 years from repayment start date"
  },
  {
    type: "Plan 5",
    who: "English/Welsh students who started from Sept 2023 onwards",
    threshold: "£25,000 per year",
    interest: "RPI + 0% during studies, then varies with RPI",
    writeoff: "40 years from repayment start date"
  },
  {
    type: "Postgraduate Loan",
    who: "Students on Master's or PhD courses",
    threshold: "£21,000 per year",
    interest: "RPI + 3%",
    writeoff: "30 years from repayment start date"
  }
];

const differenceSections = [
  {
    title: "Repayment Rate",
    content: (
      <>
        <p>
          All undergraduate loans (Plan 1, 2, 4, and 5) use the same repayment rate of 9% on income above the threshold.
          Postgraduate loans use a 6% repayment rate on income above £21,000.
        </p>
      </>
    )
  },
  {
    title: "Interest Rates",
    content: (
      <>
        <p>Interest rates vary significantly between plans:</p>
        <ul className="list-disc ml-6">
          <li>Plan 1 &amp; 4: Lower rates based on RPI or Bank of England base rate + 1% (whichever is lower)</li>
          <li>Plan 2: Higher variable rates from RPI+3%</li>
          <li>Plan 5: RPI+0% while studying, then varies with RPI</li>
          <li>Postgraduate: Fixed at RPI+3%</li>
        </ul>
      </>
    )
  },
  {
    title: "Write-off Periods",
    content: (
      <>
        <p>Any remaining loan balance is cancelled after:</p>
        <ul className="list-disc ml-6">
          <li>Plan 1: 25 years</li>
          <li>Plan 2, 4 &amp; Postgraduate: 30 years</li>
          <li>Plan 5: 40 years</li>
        </ul>
        <p className="mt-1">
          The longer write-off period for Plan 5 means more graduates will repay in full.
        </p>
      </>
    )
  }
];

const UKStudentLoanPlansPage = () => (
  <div className="min-h-screen bg-gray-50 pb-16">
    {/* Breadcrumbs */}
    {/* <div className="container mx-auto text-sm text-gray-500 py-2 px-4">
      <span>Home</span> <span className="mx-1">›</span> <span>Plans</span>
    </div> */}

    {/* Header */}
    <div className="rounded-2xl bg-blue-50 container mx-auto px-8 py-8 mb-8">
      <h1 className="text-3xl md:text-4xl font-bold mb-2 text-gray-900">
        UK Student Loan Plans Explained
      </h1>
      <p className="text-gray-700 text-lg max-w-3xl">
        Compare the different student loan plans available in the UK to understand which applies to you and how they work.
      </p>
    </div>

    {/* Plans table */}
    <div className="container mx-auto px-2">
      <div className="overflow-x-auto rounded-2xl shadow-sm bg-white">
        <table className="min-w-full text-sm text-left text-gray-800">
          <thead>
            <tr className="bg-blue-50 font-bold uppercase text-gray-700 text-xs">
              <th className="py-3 px-4">Plan Type</th>
              <th className="py-3 px-4">Who It Applies To</th>
              <th className="py-3 px-4">Repayment Threshold</th>
              <th className="py-3 px-4">Interest Rate</th>
              <th className="py-3 px-4">Write-off Period</th>
              <th className="py-3 px-4"></th>
            </tr>
          </thead>
          <tbody>
            {plans.map(plan => (
              <tr className="border-b last:border-b-0" key={plan.type}>
                <td className="py-3 px-4 font-semibold">{plan.type}</td>
                <td className="py-3 px-4">{plan.who}</td>
                <td className="py-3 px-4">{plan.threshold}</td>
                <td className="py-3 px-4">{plan.interest}</td>
                <td className="py-3 px-4">{plan.writeoff}</td>
                <td className="py-3 px-4 text-blue-600 font-medium text-xs flex items-center">
                  Details <span className="ml-1">→</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Info block */}
      <div className="rounded-2xl border border-blue-100 bg-white my-8 p-6">
        <div className="font-bold mb-2 text-gray-900 text-lg">Not Sure Which Plan Applies to You?</div>
        <div className="mb-3 text-gray-700">
          Your student loan plan type depends on when and where you studied. If you’re unsure which plan you’re on:
        </div>
        <ul className="rounded-lg bg-blue-50 px-5 py-3 mb-2 text-blue-800 text-sm space-y-1">
          <li>• Check your payslip – it should mention your plan type if you’re making repayments</li>
          <li>
            • Log in to your <a href="#" className="underline text-blue-600">online student loan account</a>
          </li>
          <li>
            • Contact the <a href="#" className="underline text-blue-600">Student Loans Company</a> directly
          </li>
        </ul>
        <div className="text-sm text-blue-800">
          If you studied at different times, you might have multiple loan plans. Use our <a href="#" className="underline text-blue-600">Combined Repayment Calculator</a> to understand how multiple loans are repaid.
        </div>
      </div>
    </div>

    {/* Key differences section */}
    <div className="container mx-auto px-2">
    <div className="font-bold text-lg mb-5 mt-10 text-gray-900">
      Key Differences Between Student Loan Plans
    </div>
    <div className="space-y-6">
      {differenceSections.map(d => (
        <div key={d.title} className="rounded-2xl bg-white border border-gray-100 p-5">
          <div className="font-bold text-base mb-1 text-gray-900">{d.title}</div>
          <div className="text-gray-800 text-[15px]">{d.content}</div>
        </div>
      ))}
    </div>
    </div>

    {/* CTA section */}
    <div className="container mx-auto mt-10 px-2">
      <div className="rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 shadow px-8 py-8 text-white text-center">
        <div className="text-2xl md:text-3xl font-bold mb-2">Calculate Your Loan Repayments</div>
        <p className="mb-6 text-base">
          Use our suite of calculators to understand exactly how much you'll repay each month and over the lifetime of your loan.
        </p>
        <div className="flex flex-col sm:flex-row items-center gap-4 justify-center">
          <button className="bg-white text-blue-700 font-medium rounded-md px-5 py-2 hover:bg-blue-50 border border-white transition">
            View All Calculators
          </button>
          <button className="bg-blue-700 text-white font-medium rounded-md px-5 py-2 hover:bg-blue-800 border border-blue-700 transition">
            Learn More
          </button>
        </div>
      </div>
    </div>
  </div>
);

export default UKStudentLoanPlansPage;
