import Link from 'next/link'
import React from 'react'

const Navbar = () => {
    return (
        <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
            <div className="container mx-auto px-4 py-4">
                <nav className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <span className=" text-xl font-bold text-primary">Student Loan Calculator</span>
                    </div>
                    <div className="hidden md:flex items-center gap-6 text-sm">
                        <Link href="/" className="text-gray-600 hover:text-gray-900">
                            Home
                        </Link>
                        <Link href="/calculators" className="text-gray-600 hover:text-gray-900">
                            Calculators
                        </Link>
                        <Link href="#" className="text-gray-600 hover:text-gray-900">
                            Plans
                        </Link>
                        <Link href="#" className="text-gray-600 hover:text-gray-900">
                            Guides
                        </Link>
                        <Link href="#" className="text-gray-600 hover:text-gray-900">
                            FAQs
                        </Link>
                    </div>
                </nav>
            </div>
        </header>
    )
}

export default Navbar